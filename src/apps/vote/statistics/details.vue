<template>
    <view class="vote-detail-page">
        <!-- 图片显示区域 -->
        <view class="video-container">
            <image class="video-placeholder" :src="videoPlaceholder" mode="aspectFill" />

            <view class="vote-status">
                <image src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" class="status-dot"></image>
                <text class="status-text">距离结束还剩：</text>
                <text class="countdown">
                    <uni-countdown format="HH:mm:ss" :show-second="false" :show-colon="false" :font-size="18" color="#FFFFFF" background-color="#333333" :show-day="true" :hour="12" :minute="12" :second="12" />
                </text>
            </view>
            <view class="display_title">展开投票详情</view>
        </view>

        <view>
            <!-- 投票信息区域 -->
            <view class="vote-info">
                <view class="vote-description">
                    <text class="description-text"> ...这里是一个活动说明的相关性!!! 为满足各种业务场景开发需求以及提升开发效率，我们提供了多套 基础的组件库和模板库供开发者使用。这些基础组件和模板都是经过精心设计 和优化的，不仅具有统一的视觉风格，还具备良好的用户体验，能够帮助开发者 快速构建高质量的应用。 </text>
                </view>
            </view>

            <!-- 投票选项卡片区域 -->
            <view class="vote-options">
                <uni-row class="options-grid">
                    <uni-col :span="12" v-for="(option, index) in voteOptions" :key="option.id">
                        <view class="option-card-wrapper">
                            <view class="option-card" :class="{ selected: selectedOption === option.id }" @click="selectOption(option.id)">
                                <!-- 头像区域 -->
                                <view class="avatar-container">
                                    <image class="candidate-avatar" :src="option.avatar" mode="aspectFill" />
                                </view>
                                <!-- 左上角选择标识 -->
                                <view class="selection-indicator">
                                    <radio color="#11C685" v-if="selectedOption === option.id" value="r1" :checked="true" />
                                    <radio color="#11C685" v-else value="r1" :checked="false" />
                                </view>

                                <view class="candidate-number_box">
                                    {{ option.number }}
                                </view>

                                <!-- 信息区域 -->
                                <view class="card-info">
                                    <text class="candidate-name">{{ option.name }}</text>
                                    <text class="vote-count">{{ option.voteCount }} <text class="vote-count-unit">票</text></text>
                                    <text class="candidate-description">{{ option.description }}</text>

                                    <!-- 根据类型存在的一个票数选择器 -->
                                    <uni-number-box class="number-box" @change="changeValue" />
                                </view>
                            </view>
                        </view>
                    </uni-col>
                </uni-row>
            </view>
        </view>

        <!-- 选项排名统计 -->
        <view class="rankingList">
            <view class="rankingList_title"> 选项排名统计： </view>

            <!-- 表格头 -->
            <view class="table_head">
                <view class="table_head_item table_head_option">选项名称</view>
                <view class="table_head_item table_head_voteCount">票数</view>
                <view class="table_head_item table_head_rank">排名</view>
            </view>

            <!-- 表格内容 -->
            <view class="table_content">
                <view class="table_row" v-for="(item, index) in state.voteOptions" :key="item.id" @click="handleOptionClick(item)">
                    <view class="table_row_item table_row_option">{{ item.title }}</view>
                    <view class="table_row_item table_row_voteCount">{{ item.countNum }}</view>
                    <view class="table_row_item table_row_rank" v-if="item.countNum > 0">{{ index + 1 }}</view>
                    <view class="table_row_item table_row_rank" v-else>-</view>
                </view>
            </view>
        </view>

        <!-- 底部固定按钮 -->
        <view class="bottom-actions">
            <button class="vote-button" type="primary" @click="handleVote" :disabled="!canVote || !selectedOption">投票</button>
            <button class="share-button" @click="handleShare">分享</button>
        </view>
    </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
import { ref, computed, onMounted } from "vue"

// 响应式数据定义
const days = ref(12)
const hours = ref(32)
const minutes = ref(45)

// 图片占位图
const videoPlaceholder = ref("https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png")

// 当前选中的投票选项
const selectedOption = ref(null)

const state = reactive({
    voteId: null,
    voteInfo: {},
    options: [],
    voteOptions: []
})

// 投票选项数据
// const voteOptions = ref([
//     {
//         id: 1,
//         number: 1,
//         name: "名称名称",
//         avatar: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
//         voteCount: 68,
//         description: "参赛描述参赛描述"
//     },
//     {
//         id: 2,
//         number: 2,
//         name: "名称名称",
//         avatar: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
//         voteCount: 45,
//         description: "参赛描述参赛描述"
//     },
//     {
//         id: 3,
//         number: 3,
//         name: "名称名称",
//         avatar: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
//         voteCount: 32,
//         description: "参赛描述参赛描述"
//     },
//     {
//         id: 4,
//         number: 4,
//         name: "名称名称",
//         avatar: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
//         voteCount: 28,
//         description: "参赛描述参赛描述"
//     }
// ])

// 计算是否可以投票
const canVote = computed(() => {
    // 根据投票状态判断，这里假设投票尚未开始所以不能投票
    return false
})

/**
 * 选择投票选项
 * @param {number} optionId - 选项ID
 */
const selectOption = (optionId) => {
    console.log("选择了投票选项：", optionId)
    selectedOption.value = optionId

    // 添加触觉反馈
    uni.vibrateShort()

    // 显示选择提示
    uni.showToast({
        title: `已选择${optionId}号选手`,
        icon: "none",
        duration: 1500
    })
}

/**
 * 处理投票提交
 */
const handleVote = () => {
    if (!selectedOption.value) {
        uni.showToast({
            title: "请先选择一个选手",
            icon: "none"
        })
        return
    }

    if (!canVote.value) {
        uni.showToast({
            title: "投票尚未开始",
            icon: "none"
        })
        return
    }

    // 显示确认对话框
    uni.showModal({
        title: "确认投票",
        content: `确定为${selectedOption.value}号选手投票吗？`,
        success: function (res) {
            if (res.confirm) {
                // 执行投票逻辑
                console.log("投票给选手：", selectedOption.value)
                uni.showToast({
                    title: "投票成功",
                    icon: "success"
                })

                // 这里可以调用API提交投票
                // submitVote(selectedOption.value)
            }
        }
    })
}

/**
 * 处理分享功能
 */
const handleShare = () => {
    // 分享功能实现
    uni.showActionSheet({
        itemList: ["微信好友", "微信朋友圈", "QQ好友", "复制链接"],
        success: function (res) {
            console.log("选择了第" + (res.tapIndex + 1) + "个分享方式")

            const shareData = {
                title: "投票活动",
                summary: "快来参与投票活动吧！",
                href: "https://example.com/vote",
                imageUrl: videoPlaceholder.value
            }

            switch (res.tapIndex) {
                case 0:
                    // 微信好友分享逻辑
                    console.log("分享到微信好友", shareData)
                    break
                case 1:
                    // 微信朋友圈分享逻辑
                    console.log("分享到微信朋友圈", shareData)
                    break
                case 2:
                    // QQ好友分享逻辑
                    console.log("分享到QQ好友", shareData)
                    break
                case 3:
                    // 复制链接
                    uni.setClipboardData({
                        data: shareData.href,
                        success: function () {
                            uni.showToast({
                                title: "链接已复制",
                                icon: "success"
                            })
                        }
                    })
                    break
            }
        }
    })
}

const handleOptionClick = (item) => {
    uni.navigateTo({
        url: `/apps/vote/statistics/option?optionId=${item.id} &title=${item.title}`
    })
}
// 页面加载时初始化
onMounted(() => {
    console.log("投票详情页面加载完成")
    // 这里可以添加页面初始化逻辑，比如获取投票数据等
})

const getCountVoteOption = () => {
    http.get("/app/vote/countVoteOption", { id: state.voteId }).then((res) => {
        state.voteOptions = res.data
    })
}

onLoad((options) => {
    state.voteId = options.voteId
    getCountVoteOption()
})
</script>

<style lang="scss" scoped>
.vote-detail-page {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding-bottom: 200rpx; // 为底部固定按钮预留空间
}

// 视频播放区域样式
.video-container {
    position: relative;

    padding: 30rpx;

    background-color: #ffffff;
    margin-bottom: 20rpx;

    .video-placeholder {
        width: 100%;
        height: 280rpx;
        border-radius: 20rpx;
    }
    .vote-status {
        display: flex;
        align-items: center;
        padding-top: 20rpx;
        padding-bottom: 20rpx;

        .status-dot {
            width: 50rpx;
            height: 50rpx;

            margin-right: 16rpx;
        }

        .status-text {
            font-weight: 600;
            font-size: 36rpx;
            color: #333333;
            margin-right: 20rpx;
        }

        .countdown {
            font-size: 28rpx;
            color: #666;
        }
    }
    .display_title {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 24rpx;
        color: #11c685;
    }
}

// 投票信息区域样式
.vote-info {
    background-color: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .vote-description {
        .description-text {
            font-size: 26rpx;
            color: #666;
            line-height: 1.6;
        }
    }
}

// 投票选项卡片区域样式
.vote-options {
    padding: 0 20rpx;

    .options-grid {
        .option-card-wrapper {
            margin: 10rpx;

            .option-card {
                position: relative;
                background-color: #fff;
                border-radius: 20rpx;

                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
                transition: all 0.3s ease;
                border: 3rpx solid transparent;

                // 选中状态样式
                &.selected {
                    border-color: #07c160;
                    transform: scale(1.02);
                    box-shadow: 0 6rpx 20rpx rgba(7, 193, 96, 0.2);
                }

                // 左上角选择标识
                .selection-indicator {
                    position: absolute;
                    top: 20rpx;
                    left: 20rpx;
                    // z-index: 10;
                }

                // 头像容器
                .avatar-container {
                    display: flex;
                    justify-content: center;
                    .candidate-avatar {
                        height: 223rpx;
                        border-radius: 20rpx;
                    }
                }

                // 信息区域
                .card-info {
                    text-align: center;

                    padding: 30rpx;

                    .candidate-name {
                        display: block;

                        margin-bottom: 8rpx;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #333333;
                    }

                    .vote-count {
                        display: block;
                        font-size: 28rpx;
                        color: #11c685;
                        font-weight: 400;
                        margin-bottom: 12rpx;
                        .vote-count-unit {
                            color: #333333;
                        }
                    }

                    .candidate-description {
                        display: block;
                        margin-bottom: 12rpx;
                        font-weight: 400;
                        font-size: 26rpx;
                        color: #333333;
                    }
                }

                // 点击效果
                &:active {
                    transform: scale(0.98);
                }
            }
        }
    }
}

// 底部固定按钮样式
.bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;

    padding-top: 30rpx;
    padding-right: 30rpx;
    padding-bottom: calc(44rpx + env(safe-area-inset-bottom));
    padding-left: 30rpx;
    border-top: 1rpx solid #e5e5e5;
    display: flex;
    gap: 20rpx;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);

    .vote-button {
        flex: 1;
        background-color: #07c160;
        color: #fff;
        border: none;
        border-radius: 10rpx;
        height: 92rpx;
        font-size: 32rpx;
        font-weight: 400;

        &[disabled] {
            background-color: #c8c9cc;
            color: #999;
        }

        &:not([disabled]):active {
            background-color: #05a652;
            transform: scale(0.98);
        }
    }

    .share-button {
        flex: 1;
        background-color: #fff;
        color: #333;
        border: 2rpx solid #e5e5e5;
        border-radius: 12rpx;
        height: 88rpx;
        font-size: 32rpx;

        &:active {
            background-color: #f8f9fa;
            transform: scale(0.98);
        }
    }
}

// 隐藏秒
:deep(.uni-countdown) {
    uni-text:nth-last-of-type(1) {
        display: none !important;
    }
    uni-text:nth-last-of-type(2) {
        display: none !important;
    }
}

.candidate-number_box {
    position: absolute;
    width: 88rpx;
    height: 40rpx;
    background: #11c685;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 26rpx;
    color: #ffffff;
    top: 204rpx;
    left: 120rpx;
}

.number-box {
    display: flex;
    align-items: center;
    justify-content: center;
}

.rankingList {
    background: #ffffff;
    padding: 30rpx 30rpx 0 30rpx;
    .rankingList_title {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        padding-bottom: 30rpx;
    }
}

.table_head {
    display: flex;
    background: #f3fcf9;
    border-radius: 10rpx;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    .table_head_item {
        flex: 1;
        text-align: left;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }
    .table_head_rank {
        flex: 0 0 10%;
    }
    .table_head_option {
        flex: 0 0 40%;
    }
}

.table_content {
    .table_row {
        display: flex;
        padding: 20rpx 30rpx;
        border-bottom: 1rpx solid #f5f5f5;
        .table_row_item {
            flex: 1;
            text-align: left;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
        }
        .table_row_rank {
            flex: 0 0 10%;
        }
        .table_row_option {
            flex: 0 0 40%;
        }
    }
}
</style>
