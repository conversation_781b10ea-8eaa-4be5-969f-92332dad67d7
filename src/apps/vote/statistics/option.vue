<template>
    <view class="optionPage">
        <view class="line_box"></view>
        <view class="opt_box">
            <view class="optionPage_title">
                选项名称： <text class="optionPage_title_name"> {{ state.title }} </text>
            </view>
            <!-- 表格头 -->
            <view class="table_head">
                <view class="table_head_item table_head_option">投票人</view>

                <view class="table_head_item table_head_rank">有效投票次数</view>
            </view>

            <!-- 表格内容 -->
            <view class="table_content">
                <view class="table_row" v-for="(item, index) in state.list" :key="item.id">
                    <view class="table_row_item table_row_option">{{ item.name }}</view>
                    <view class="table_row_item table_row_rank">{{ index + 1 }}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const state = reactive({
    voteOptionId: null,
    title: "",
    list: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
})

onLoad((options) => {
    console.log(options, "options")
    state.voteOptionId = options.optionId
    state.title = options.title
})
</script>

<style lang="scss" scoped>
.optionPage {
    min-height: 100vh;
    background: #f9faf9;
}
.line_box {
    height: 20rpx;
    background: #f9faf9;
}

.opt_box {
    padding: 30rpx;
    background: #ffffff;
}

.optionPage_title {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    padding-bottom: 20rpx;
}
.optionPage_title_name {
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
}

.table_head {
    display: flex;
    background: #f3fcf9;
    border-radius: 10rpx;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    .table_head_item {
        flex: 1;
        text-align: left;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }
    .table_head_option {
        flex: 0 0 70%;
    }
    .table_head_rank {
        flex: 0 0 30%;
    }
}

.table_content {
    .table_row {
        display: flex;
        padding: 20rpx 30rpx;
        border-bottom: 1rpx solid #f5f5f5;
        .table_row_item {
            flex: 1;
            text-align: left;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
        }
        .table_row_rank {
            flex: 0 0 30%;
        }
        .table_row_option {
            flex: 0 0 70%;
        }
    }
}
</style>
