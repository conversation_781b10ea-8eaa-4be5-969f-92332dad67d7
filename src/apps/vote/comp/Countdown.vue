<template>
    <view class="countdown-container">
        <!-- 天数显示 -->
        <view v-if="showDay && timeData.days > 0" class="time-unit">
            <text class="time-number">{{ formatNumber(timeData.days) }}</text>
            <text class="time-label">天</text>
        </view>

        <!-- 小时显示 -->
        <view v-if="showHour" class="time-unit">
            <text class="time-number">{{ formatNumber(timeData.hours) }}</text>
            <text class="time-label">时</text>
        </view>

        <!-- 分钟显示 -->
        <view v-if="showMinute" class="time-unit">
            <text class="time-number">{{ formatNumber(timeData.minutes) }}</text>
            <text class="time-label">分</text>
        </view>

        <!-- 秒数显示 -->
        <view v-if="showSecond" class="time-unit">
            <text class="time-number">{{ formatNumber(timeData.seconds) }}</text>
            <text class="time-label">秒</text>
        </view>
    </view>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from "vue"
import dayjs from "dayjs"
import duration from "dayjs/plugin/duration"
dayjs.extend(duration)

// 定义组件属性
const props = defineProps({
    // 结束时间，可以是时间戳或时间字符串
    endTime: {
        type: [String, Number],
        required: true
    },
    // 是否显示天数
    showDay: {
        type: Boolean,
        default: true
    },
    // 是否显示小时
    showHour: {
        type: Boolean,
        default: true
    },
    // 是否显示分钟
    showMinute: {
        type: Boolean,
        default: true
    },
    // 是否显示秒数
    showSecond: {
        type: Boolean,
        default: true
    },
    // 数字颜色
    numberColor: {
        type: String,
        default: "#333333"
    },
    // 标签颜色
    labelColor: {
        type: String,
        default: "#333333"
    },
    // 数字字体大小
    numberSize: {
        type: String,
        default: "26rpx"
    },
    // 标签字体大小
    labelSize: {
        type: String,
        default: "26rpx"
    },
    backgroundColor: {
        type: String,
        default: "transparent"
    }
})

/**
 * 格式化数字为两位数
 */
const formatNumber = (num) => {
    return String(num).padStart(2, "0")
}

// 定义事件
const emit = defineEmits(["timeup"])

// 倒计时数据
const timeData = reactive({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
})

// 定时器引用
let timer = null

/**
 * 计算剩余时间
 */
const calculateTimeLeft = () => {
    try {
        // 获取当前时间
        const now = dayjs()

        // 解析结束时间
        let endDateTime
        if (typeof props.endTime === "number") {
            // 如果是时间戳
            endDateTime = dayjs(props.endTime)
        } else {
            // 如果是时间字符串
            endDateTime = dayjs(props.endTime)
        }

        // 计算时间差（毫秒）
        const diff = endDateTime.diff(now)

        if (diff <= 0) {
            // 时间已到，清零并触发事件
            timeData.days = 0
            timeData.hours = 0
            timeData.minutes = 0
            timeData.seconds = 0

            // 清除定时器
            if (timer) {
                clearInterval(timer)
                timer = null
            }

            // 触发时间到达事件
            emit("timeup")
            return
        }

        // 计算各个时间单位
        const duration = dayjs.duration(diff)
        timeData.days = Math.floor(duration.asDays())
        timeData.hours = duration.hours()
        timeData.minutes = duration.minutes()
        timeData.seconds = duration.seconds()
    } catch (error) {
        console.error("倒计时计算错误:", error)
        // 出错时清零
        timeData.days = 0
        timeData.hours = 0
        timeData.minutes = 0
        timeData.seconds = 0
    }
}

/**
 * 启动倒计时
 */
const startCountdown = () => {
    // 先计算一次
    calculateTimeLeft()

    // 启动定时器，每秒更新一次
    timer = setInterval(() => {
        calculateTimeLeft()
    }, 1000)
}

/**
 * 停止倒计时
 */
const stopCountdown = () => {
    if (timer) {
        clearInterval(timer)
        timer = null
    }
}

// 监听结束时间变化，重新启动倒计时
watch(
    () => props.endTime,
    () => {
        stopCountdown()
        startCountdown()
    },
    { immediate: false }
)

// 组件挂载时启动倒计时
onMounted(() => {
    startCountdown()
})

// 组件卸载时清除定时器
onUnmounted(() => {
    stopCountdown()
})
</script>

<style scoped>
.countdown-container {
    display: flex;
    align-items: center;
    gap: 8rpx;
}

.time-unit {
    display: flex;
    align-items: baseline;
    gap: 2rpx;
}

.time-number {
    font-size: v-bind("props.numberSize");
    color: v-bind("props.numberColor");
    background: v-bind("props.backgroundColor");
    font-weight: 500;
    min-width: 1em;
    text-align: center;
}

.time-label {
    font-size: v-bind("props.labelSize");
    color: v-bind("props.labelColor");
}
</style>
