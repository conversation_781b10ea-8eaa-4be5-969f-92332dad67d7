<template>
    <div class="DropDownCom" v-if="romKey">
        <uv-drop-down
            ref="DropDownComRef"
            :defaultValue="defaultValue"
            :sign="romKey"
            text-color="#333333"
            text-size="28rpx"
            text-active-size="28rpx"
            :customStyle="{ 'border-bottom': 'none' }"
            text-active-color="#00B781"
            :extra-icon="{
                name: 'arrow-down-fill',
                color: '#666',
                size: '26rpx'
            }"
            :extra-active-icon="{
                name: 'arrow-up-fill',
                color: '#00B781',
                size: '26rpx'
            }"
            @click="selectMenu"
        >
            <uv-drop-down-item v-for="(item, key) in menuList" :name="key" :key="key" type="2" :label="dropItem(key).label" :value="dropItem(key).value"> </uv-drop-down-item>
        </uv-drop-down>
        <uv-drop-down-popup :sign="romKey" ref="DropDownPopupRef" :click-overlay-on-close="true" :currentDropItem="currentDropItem" @clickItem="clickItem">
            <slot :name="name"></slot>
        </uv-drop-down-popup>
    </div>
</template>
<script setup>
function generateRandomID() {
    const date = +new Date()
    const num = date * (Math.random() * 1000).toFixed(0)
    return num.toString(36).substring(2)
}

const name = ref("")
const emit = defineEmits(["clickItem"])
const flag = false
const DropDownComRef = ref(null)
const DropDownPopupRef = ref(null)
const selectMenuItem = ref({})
const slotList = ref([])
const isSlot = ref(false)
const romKey = ref("")
romKey.value = generateRandomID()
const props = defineProps({
    menu: {
        type: Object,
        default: () => {
            return {}
        }
    }
})
const menuList = computed(() => {
    const menuLists = {}
    for (const key in props.menu) {
        menuLists[key] = props.menu[key]
        menuLists[key].activeColor = "#00B781"
        menuLists[key].value = "all"
        if (!props.menu[key].child) {
            slotList.value.push(key)
        }
    }
    return menuLists
})
const defaultValue = ref(["0", "all"])
const selectMenu = (item) => {
    isSlot.value = !props.menu[item.name].child
    DropDownComRef.value.init()
    name.value = item.name
}
const clickItem = (item) => {
    selectMenuItem.value[name.value] = item
    emit("clickItem", { name: name.value, ...item })
}
const currentDropItem = computed(() => {
    return props.menu[name.value]
})
const dropItem = computed(() => {
    return (key) => {
        if (selectMenuItem.value[key]) {
            return selectMenuItem.value[key]
        } else {
            return props.menu[key]
        }
    }
})
const getRefs = () => {
    return [DropDownComRef, DropDownPopupRef]
}
defineExpose({ getRefs })
</script>
<style lang="scss">
:deep(.uv-sticky) {
    z-index: 90 !important;
}
</style>
